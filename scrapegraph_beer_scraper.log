2025-06-14 20:25:51,527 - INFO - Using Together AI with Llama 3.1 70B model
2025-06-14 20:29:21,304 - INFO - Using provided Together AI API key
2025-06-14 20:29:21,305 - INFO - Using Together AI with Llama 3.3 70B Instruct Turbo Free model
2025-06-14 20:29:21,306 - INFO - Starting scrape for Tesco
2025-06-14 20:29:24,617 - INFO - Starting Tesco beer scraping with Scrapegraph AI...
2025-06-14 20:29:24,617 - INFO - Scraping Tesco page 1: https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer?page=1
2025-06-14 20:29:24,618 - ERROR - Error scraping Tesco page 1: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,619 - INFO - Scraping Tesco page 2: https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer?page=2
2025-06-14 20:29:24,620 - ERROR - Error scraping Tesco page 2: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,620 - INFO - Scraping Tesco page 3: https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer?page=3
2025-06-14 20:29:24,621 - ERROR - Error scraping Tesco page 3: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,621 - INFO - Scraping Tesco page 4: https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer?page=4
2025-06-14 20:29:24,622 - ERROR - Error scraping Tesco page 4: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,622 - INFO - Scraping Tesco page 5: https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer?page=5
2025-06-14 20:29:24,622 - ERROR - Error scraping Tesco page 5: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,623 - INFO - Total products extracted from Tesco: 0
2025-06-14 20:29:24,624 - WARNING - No products found for Tesco
2025-06-14 20:29:24,624 - INFO - Starting scrape for ASDA
2025-06-14 20:29:24,624 - INFO - Starting ASDA beer scraping with Scrapegraph AI...
2025-06-14 20:29:24,625 - INFO - Scraping ASDA page 1: https://groceries.asda.com/search/beer?page=1
2025-06-14 20:29:24,626 - ERROR - Error scraping ASDA page 1: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,627 - INFO - Scraping ASDA page 2: https://groceries.asda.com/search/beer?page=2
2025-06-14 20:29:24,627 - ERROR - Error scraping ASDA page 2: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,628 - INFO - Scraping ASDA page 3: https://groceries.asda.com/search/beer?page=3
2025-06-14 20:29:24,629 - ERROR - Error scraping ASDA page 3: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,629 - INFO - Scraping ASDA page 4: https://groceries.asda.com/search/beer?page=4
2025-06-14 20:29:24,630 - ERROR - Error scraping ASDA page 4: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,632 - INFO - Scraping ASDA page 5: https://groceries.asda.com/search/beer?page=5
2025-06-14 20:29:24,633 - ERROR - Error scraping ASDA page 5: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,634 - INFO - Total products extracted from ASDA: 0
2025-06-14 20:29:24,634 - WARNING - No products found for ASDA
2025-06-14 20:29:24,635 - INFO - Starting scrape for Sainsbury's
2025-06-14 20:29:24,635 - INFO - Starting Sainsbury's beer scraping with Scrapegraph AI...
2025-06-14 20:29:24,635 - INFO - Scraping Sainsbury's page 1: https://www.sainsburys.co.uk/gol-ui/SearchResults/beer?page=1
2025-06-14 20:29:24,636 - ERROR - Error scraping Sainsbury's page 1: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,636 - INFO - Scraping Sainsbury's page 2: https://www.sainsburys.co.uk/gol-ui/SearchResults/beer?page=2
2025-06-14 20:29:24,636 - ERROR - Error scraping Sainsbury's page 2: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,637 - INFO - Scraping Sainsbury's page 3: https://www.sainsburys.co.uk/gol-ui/SearchResults/beer?page=3
2025-06-14 20:29:24,637 - ERROR - Error scraping Sainsbury's page 3: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,637 - INFO - Scraping Sainsbury's page 4: https://www.sainsburys.co.uk/gol-ui/SearchResults/beer?page=4
2025-06-14 20:29:24,637 - ERROR - Error scraping Sainsbury's page 4: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,638 - INFO - Scraping Sainsbury's page 5: https://www.sainsburys.co.uk/gol-ui/SearchResults/beer?page=5
2025-06-14 20:29:24,638 - ERROR - Error scraping Sainsbury's page 5: Provider meta-llama is not supported.
                             If possible, try to use a model instance instead.
2025-06-14 20:29:24,638 - INFO - Total products extracted from Sainsbury's: 0
2025-06-14 20:29:24,638 - WARNING - No products found for Sainsbury's
2025-06-14 20:29:24,638 - INFO - Total products scraped: 0
