#!/usr/bin/env python3
"""
Usage Guide for Comprehensive Beer Scraper
==========================================

This script provides examples and guidance on how to use the beer scraper effectively.
"""

def print_usage_guide():
    """Print comprehensive usage guide"""
    
    print("🍺 COMPREHENSIVE BEER SCRAPER - USAGE GUIDE")
    print("=" * 60)
    
    print("\n📋 BASIC USAGE:")
    print("   python comprehensive_beer_scraper.py")
    print("   → Scrapes all beer products and creates individual entries")
    
    print("\n🔧 COMMAND LINE OPTIONS:")
    print("   --retailers tesco asda     → Scrape specific retailers only")
    print("   --no-individual           → Keep multi-packs as single entries")
    print("   --analyze results.csv     → Analyze existing CSV file")
    
    print("\n📁 OUTPUT FILES:")
    print("   exhaustive_beer_list_YYYYMMDD_HHMMSS.csv  → Main results")
    print("   exhaustive_beer_list_YYYYMMDD_HHMMSS.json → JSON format")
    print("   beer_scraper.log                          → Detailed logs")
    
    print("\n🍻 WHAT MAKES IT 'EXHAUSTIVE':")
    print("   ✓ Each pack size becomes individual entries")
    print("   ✓ 4-pack = 4 separate beer entries")
    print("   ✓ 12-pack = 12 separate beer entries")
    print("   ✓ 24-pack = 24 separate beer entries")
    print("   ✓ Single bottles/cans = 1 entry each")
    
    print("\n📊 DATA EXTRACTED FOR EACH BEER:")
    print("   • Product name and brand")
    print("   • Pack size (1 unit, 4 pack, etc.)")
    print("   • Volume in ml (330ml, 440ml, 500ml, etc.)")
    print("   • Alcohol content percentage")
    print("   • Price per individual unit")
    print("   • Retailer and product URL")
    print("   • Availability status")
    print("   • Scraping timestamp")
    
    print("\n🚀 EXAMPLE SCENARIOS:")
    print("\n   Scenario 1: Complete beer inventory")
    print("   → python comprehensive_beer_scraper.py")
    print("   → Gets ALL beers as individual entries")
    
    print("\n   Scenario 2: Tesco only, keep packs together")
    print("   → python comprehensive_beer_scraper.py --retailers tesco --no-individual")
    print("   → Multi-packs stay as single entries")
    
    print("\n   Scenario 3: Analyze existing data")
    print("   → python comprehensive_beer_scraper.py --analyze my_beer_data.csv")
    print("   → Shows statistics and insights")
    
    print("\n⚠️  IMPORTANT NOTES:")
    print("   • Respects rate limits (1.5s delay between requests)")
    print("   • Handles network errors gracefully")
    print("   • May be blocked by some retailers' anti-bot measures")
    print("   • Always check robots.txt and terms of service")
    
    print("\n🛠️  TROUBLESHOOTING:")
    print("   Problem: Network timeouts")
    print("   → Check internet connection")
    print("   → Try running at different times")
    print("   → Some retailers block automated requests")
    
    print("\n   Problem: Empty results")
    print("   → Website structure may have changed")
    print("   → Check beer_scraper.log for details")
    print("   → Try the demo: python demo_scraper.py")
    
    print("\n   Problem: Import errors")
    print("   → Install dependencies: pip install -r requirements.txt")
    print("   → Activate conda environment if needed")
    
    print("\n🎯 EXPECTED RESULTS:")
    print("   • Typical run: 1,000-3,000+ individual beer entries")
    print("   • Processing time: 5-30 minutes depending on retailer")
    print("   • File sizes: CSV ~500KB-2MB, JSON ~1-5MB")
    
    print("\n📈 ANALYSIS FEATURES:")
    print("   • Brand distribution analysis")
    print("   • Price range and averages")
    print("   • Pack size popularity")
    print("   • Alcohol content statistics")
    print("   • Export to Excel-compatible CSV")
    
    print("\n🔄 WORKFLOW RECOMMENDATIONS:")
    print("   1. Run demo first: python demo_scraper.py")
    print("   2. Test with single retailer: --retailers tesco")
    print("   3. Run full scrape during off-peak hours")
    print("   4. Analyze results: --analyze your_results.csv")
    print("   5. Import CSV into Excel/Google Sheets for further analysis")
    
    print("\n" + "=" * 60)
    print("Ready to scrape some beer data? 🍻")

if __name__ == "__main__":
    print_usage_guide()
