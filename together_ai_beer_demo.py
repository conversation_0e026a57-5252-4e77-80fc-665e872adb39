#!/usr/bin/env python3
"""
Together AI Beer Scraper Demo
============================

This script demonstrates the beer scraper using Together AI's free Llama 3.3 70B model.
"""

import os
import json
from datetime import datetime

def show_together_ai_advantages():
    """Show advantages of using Together AI"""
    
    print("🤖 TOGETHER AI ADVANTAGES")
    print("=" * 50)
    
    advantages = [
        "🆓 **FREE** - Llama 3.3 70B Instruct Turbo Free model",
        "⚡ **FAST** - Optimized inference infrastructure", 
        "🧠 **SMART** - 70B parameter model with excellent reasoning",
        "🔄 **RELIABLE** - High uptime and consistent performance",
        "📊 **QUALITY** - Better extraction than smaller models",
        "🌐 **ACCESSIBLE** - No local setup required",
        "💰 **COST-EFFECTIVE** - No API costs for the free model"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")

def show_beer_scraping_process():
    """Show how the beer scraping process works"""
    
    print("\n🍺 BEER SCRAPING PROCESS")
    print("=" * 50)
    
    print("1️⃣ **AI-Powered Page Analysis**")
    print("   • Llama 3.3 70B analyzes webpage content")
    print("   • Understands product structure semantically")
    print("   • Identifies beer products intelligently")
    
    print("\n2️⃣ **Smart Information Extraction**")
    print("   • Product names and brands")
    print("   • Pack sizes (1, 4, 6, 12, 24 units)")
    print("   • Volume per unit (ml)")
    print("   • Alcohol content (%)")
    print("   • Prices and availability")
    
    print("\n3️⃣ **Individual Entry Creation**")
    print("   • Multi-packs split into individual beers")
    print("   • 4-pack → 4 separate beer entries")
    print("   • 12-pack → 12 separate beer entries")
    print("   • Price per unit calculated automatically")
    
    print("\n4️⃣ **Multi-Retailer Support**")
    print("   • Tesco, ASDA, Sainsbury's")
    print("   • Same AI prompt works across different sites")
    print("   • Adapts to different HTML structures")

def show_sample_ai_prompt():
    """Show the AI prompt used for beer extraction"""
    
    print("\n📝 AI PROMPT EXAMPLE")
    print("=" * 50)
    
    prompt = """
    Extract ALL beer products from this webpage with detailed information:
    
    For each beer product found, provide:
    1. Product name (full name including brand)
    2. Brand name (e.g., Stella Artois, Heineken)
    3. Pack size (e.g., "1 unit", "4 pack", "12 pack")
    4. Volume per unit in milliliters
    5. Alcohol content percentage
    6. Price in GBP
    7. Product URL and image URL
    8. Availability status
    
    IMPORTANT: If a product is a multi-pack, create SEPARATE entries 
    for each individual beer with pack_size "1 unit" and individual price.
    
    Return as JSON array where each object = ONE individual beer unit.
    """
    
    print("🤖 **AI Prompt Preview:**")
    print(prompt)
    
    print("\n💡 **Why This Works:**")
    print("   • Clear, specific instructions")
    print("   • Emphasizes individual entry creation")
    print("   • Structured JSON output format")
    print("   • Works across different website layouts")

def show_expected_output():
    """Show expected output format"""
    
    print("\n📊 EXPECTED OUTPUT FORMAT")
    print("=" * 50)
    
    sample_output = [
        {
            "name": "Stella Artois Premium Lager Beer 4 x 440ml (Unit 1 of 4)",
            "brand": "Stella Artois",
            "pack_size": "1 unit",
            "volume_ml": 440,
            "alcohol_content": 5.0,
            "price": 1.13,
            "price_per_unit": 1.13,
            "retailer": "Tesco",
            "product_url": "https://www.tesco.com/groceries/...",
            "availability": "Available",
            "scraped_at": "2025-06-14T20:30:00"
        },
        {
            "name": "Stella Artois Premium Lager Beer 4 x 440ml (Unit 2 of 4)",
            "brand": "Stella Artois", 
            "pack_size": "1 unit",
            "volume_ml": 440,
            "alcohol_content": 5.0,
            "price": 1.13,
            "price_per_unit": 1.13,
            "retailer": "Tesco",
            "product_url": "https://www.tesco.com/groceries/...",
            "availability": "Available",
            "scraped_at": "2025-06-14T20:30:00"
        }
    ]
    
    print("🍻 **Sample Individual Beer Entries:**")
    print(json.dumps(sample_output, indent=2))
    
    print(f"\n📈 **Scaling Example:**")
    print("   Original: 'Heineken 12-pack' → 1 product entry")
    print("   AI Result: 12 individual Heineken beer entries")
    print("   True beer count achieved! 🎯")

def show_usage_instructions():
    """Show how to use the scraper"""
    
    print("\n🚀 USAGE INSTRUCTIONS")
    print("=" * 50)
    
    print("1️⃣ **Run the Main Scraper:**")
    print("   python scrapegraph_beer_scraper.py")
    print("   → Choose option 1 (Together AI)")
    print("   → Uses your provided API key automatically")
    
    print("\n2️⃣ **Test First (Optional):**")
    print("   python test_together_ai.py")
    print("   → Verifies Together AI connection")
    print("   → Tests basic functionality")
    
    print("\n3️⃣ **Expected Runtime:**")
    print("   • Setup: 10-30 seconds")
    print("   • Scraping: 10-30 minutes")
    print("   • Total beers: 1,000-5,000+ individual entries")
    
    print("\n4️⃣ **Output Files:**")
    print("   • scrapegraph_beer_products_YYYYMMDD_HHMMSS.csv")
    print("   • scrapegraph_beer_products_YYYYMMDD_HHMMSS.json")
    print("   • scrapegraph_beer_scraper.log")
    
    print("\n5️⃣ **Analysis:**")
    print("   • Import CSV into Excel/Google Sheets")
    print("   • Filter by brand, volume, price")
    print("   • Create charts and pivot tables")

def main():
    """Main demo function"""
    
    print("🍺 TOGETHER AI BEER SCRAPER DEMO")
    print("=" * 70)
    print("Advanced AI-powered beer scraping with FREE Llama 3.3 70B model")
    print(f"Demo time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    show_together_ai_advantages()
    show_beer_scraping_process()
    show_sample_ai_prompt()
    show_expected_output()
    show_usage_instructions()
    
    print("\n🎉 READY TO START!")
    print("=" * 50)
    print("Your Together AI beer scraper is configured and ready!")
    print("The free Llama 3.3 70B model will provide excellent results.")
    print("\n🍻 Run this command to start scraping:")
    print("   python scrapegraph_beer_scraper.py")
    
    print("\n💡 **Pro Tips:**")
    print("   • The AI model is completely free to use")
    print("   • Results will be more accurate than traditional scraping")
    print("   • Each multi-pack becomes individual beer entries")
    print("   • Perfect for comprehensive beer market analysis")

if __name__ == "__main__":
    main()
