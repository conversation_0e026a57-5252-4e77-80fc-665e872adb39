#!/usr/bin/env python3
"""
Advanced Beer Product Scraper using Scrapegraph AI
==================================================

This script uses Scrapegraph AI to intelligently scrape beer products from multiple retailers.
It leverages AI to understand page structure and extract detailed product information,
creating individual entries for each beer in multi-packs.

Features:
- AI-powered intelligent scraping
- Multi-retailer support
- Individual entries for pack contents
- Detailed product information extraction
- JSON and CSV export
- Robust error handling

Author: Beer Scraping AI Bot
Date: 2025-06-14
"""

import os
import json
import csv
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scrapegraph_beer_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class BeerProduct:
    """Data class for beer product information"""
    name: str
    brand: str
    pack_size: str
    volume_ml: Optional[int]
    alcohol_content: Optional[float]
    price: Optional[float]
    price_per_unit: Optional[float]
    retailer: str
    product_url: str
    image_url: Optional[str]
    availability: str
    description: Optional[str]
    scraped_at: str

class ScrapegraphBeerScraper:
    """Advanced beer scraper using Scrapegraph AI"""
    
    def __init__(self, use_local_llm: bool = True):
        """
        Initialize the scraper
        
        Args:
            use_local_llm: If True, uses local Ollama model. If False, uses OpenAI API.
        """
        self.use_local_llm = use_local_llm
        self.products = []
        self.setup_config()
        
    def setup_config(self):
        """Setup Scrapegraph AI configuration"""
        if self.use_local_llm:
            # Local Ollama configuration
            self.graph_config = {
                "llm": {
                    "model": "ollama/llama3.2",
                    "temperature": 0.1,
                    "format": "json",
                    "model_tokens": 8192,
                    "base_url": "http://localhost:11434",
                },
                "verbose": True,
                "headless": True,  # Run browser in headless mode
            }
            logger.info("Using local Ollama model (llama3.2)")
        else:
            # OpenAI configuration
            openai_key = os.getenv("OPENAI_API_KEY")
            if not openai_key:
                raise ValueError("OPENAI_API_KEY environment variable not set")
            
            self.graph_config = {
                "llm": {
                    "api_key": openai_key,
                    "model": "openai/gpt-4o-mini",
                    "temperature": 0.1,
                },
                "verbose": True,
                "headless": True,
            }
            logger.info("Using OpenAI GPT-4o-mini model")
    
    def create_beer_extraction_prompt(self) -> str:
        """Create a detailed prompt for extracting beer product information"""
        return """
        Extract ALL beer products from this webpage with the following detailed information for each product:
        
        For each beer product found, provide:
        1. Product name (full name including brand and description)
        2. Brand name (e.g., Stella Artois, Heineken, Corona)
        3. Pack size information (e.g., "1 unit", "4 pack", "12 pack", "24 pack")
        4. Volume per unit in milliliters (e.g., 330, 440, 500)
        5. Alcohol content percentage (e.g., 4.5, 5.0)
        6. Price in GBP (pounds)
        7. Product URL/link
        8. Image URL if available
        9. Availability status (Available, Out of Stock, etc.)
        10. Any additional product description
        
        IMPORTANT INSTRUCTIONS:
        - If a product is a multi-pack (e.g., 4-pack, 12-pack), create SEPARATE entries for each individual beer
        - For a 4-pack, create 4 separate entries with pack_size "1 unit" and calculate individual price
        - For a 12-pack, create 12 separate entries with pack_size "1 unit" and calculate individual price
        - Extract volume information from product names (look for ml, cl, l indicators)
        - Extract alcohol content from product descriptions (look for % ABV indicators)
        - Be thorough and don't miss any beer products on the page
        
        Return the data as a JSON array where each object represents ONE individual beer unit.
        """
    
    def scrape_retailer_beers(self, retailer_name: str, base_url: str, max_pages: int = 10) -> List[Dict]:
        """
        Scrape beer products from a specific retailer using Scrapegraph AI
        
        Args:
            retailer_name: Name of the retailer
            base_url: Base URL for beer products
            max_pages: Maximum number of pages to scrape
            
        Returns:
            List of extracted beer product dictionaries
        """
        try:
            from scrapegraphai.graphs import SmartScraperGraph
        except ImportError:
            logger.error("Scrapegraph AI not installed. Install with: pip install scrapegraphai")
            logger.error("Also run: playwright install")
            return []
        
        all_products = []
        prompt = self.create_beer_extraction_prompt()
        
        logger.info(f"Starting {retailer_name} beer scraping with Scrapegraph AI...")
        
        for page in range(1, max_pages + 1):
            try:
                # Construct page URL
                if "?" in base_url:
                    url = f"{base_url}&page={page}"
                else:
                    url = f"{base_url}?page={page}"
                
                logger.info(f"Scraping {retailer_name} page {page}: {url}")
                
                # Create SmartScraperGraph instance
                smart_scraper = SmartScraperGraph(
                    prompt=prompt,
                    source=url,
                    config=self.graph_config
                )
                
                # Run the scraper
                result = smart_scraper.run()
                
                if not result or not isinstance(result, (list, dict)):
                    logger.warning(f"No valid data returned from {retailer_name} page {page}")
                    continue
                
                # Handle different result formats
                if isinstance(result, dict):
                    if 'products' in result:
                        products = result['products']
                    elif 'beers' in result:
                        products = result['beers']
                    else:
                        products = [result]  # Single product
                elif isinstance(result, list):
                    products = result
                else:
                    logger.warning(f"Unexpected result format from {retailer_name} page {page}")
                    continue
                
                if not products:
                    logger.info(f"No products found on {retailer_name} page {page} - might be end of results")
                    break
                
                # Add retailer information to each product
                for product in products:
                    if isinstance(product, dict):
                        product['retailer'] = retailer_name
                        product['scraped_at'] = datetime.now().isoformat()
                        all_products.append(product)
                
                logger.info(f"Extracted {len(products)} products from {retailer_name} page {page}")
                
                # Respectful delay between requests
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error scraping {retailer_name} page {page}: {e}")
                continue
        
        logger.info(f"Total products extracted from {retailer_name}: {len(all_products)}")
        return all_products
    
    def normalize_product_data(self, raw_products: List[Dict]) -> List[BeerProduct]:
        """
        Normalize raw product data into BeerProduct objects
        
        Args:
            raw_products: List of raw product dictionaries
            
        Returns:
            List of normalized BeerProduct objects
        """
        normalized_products = []
        
        for product in raw_products:
            try:
                # Extract and normalize fields
                name = str(product.get('name', product.get('product_name', 'Unknown Product')))
                brand = str(product.get('brand', product.get('brand_name', 'Unknown Brand')))
                pack_size = str(product.get('pack_size', '1 unit'))
                
                # Handle volume
                volume_ml = product.get('volume_ml', product.get('volume'))
                if volume_ml and isinstance(volume_ml, str):
                    try:
                        volume_ml = int(''.join(filter(str.isdigit, volume_ml)))
                    except:
                        volume_ml = None
                
                # Handle alcohol content
                alcohol_content = product.get('alcohol_content', product.get('abv'))
                if alcohol_content and isinstance(alcohol_content, str):
                    try:
                        alcohol_content = float(''.join(c for c in alcohol_content if c.isdigit() or c == '.'))
                    except:
                        alcohol_content = None
                
                # Handle price
                price = product.get('price')
                if price and isinstance(price, str):
                    try:
                        price = float(''.join(c for c in price if c.isdigit() or c == '.'))
                    except:
                        price = None
                
                # Calculate price per unit
                price_per_unit = price  # Default for single units
                
                beer_product = BeerProduct(
                    name=name,
                    brand=brand,
                    pack_size=pack_size,
                    volume_ml=volume_ml,
                    alcohol_content=alcohol_content,
                    price=price,
                    price_per_unit=price_per_unit,
                    retailer=product.get('retailer', 'Unknown'),
                    product_url=product.get('product_url', product.get('url', '')),
                    image_url=product.get('image_url', product.get('image', '')),
                    availability=product.get('availability', 'Unknown'),
                    description=product.get('description', ''),
                    scraped_at=product.get('scraped_at', datetime.now().isoformat())
                )
                
                normalized_products.append(beer_product)
                
            except Exception as e:
                logger.warning(f"Error normalizing product data: {e}")
                continue
        
        return normalized_products

    def run_comprehensive_scrape(self) -> List[BeerProduct]:
        """
        Run comprehensive beer scraping across multiple retailers

        Returns:
            List of all scraped beer products
        """
        all_products = []

        # Define retailers to scrape
        retailers = {
            "Tesco": "https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer",
            "ASDA": "https://groceries.asda.com/search/beer",
            "Sainsbury's": "https://www.sainsburys.co.uk/gol-ui/SearchResults/beer",
        }

        for retailer_name, base_url in retailers.items():
            try:
                logger.info(f"Starting scrape for {retailer_name}")
                raw_products = self.scrape_retailer_beers(retailer_name, base_url, max_pages=5)

                if raw_products:
                    normalized_products = self.normalize_product_data(raw_products)
                    all_products.extend(normalized_products)
                    logger.info(f"Added {len(normalized_products)} products from {retailer_name}")
                else:
                    logger.warning(f"No products found for {retailer_name}")

            except Exception as e:
                logger.error(f"Error scraping {retailer_name}: {e}")
                continue

        logger.info(f"Total products scraped: {len(all_products)}")
        return all_products

    def save_to_csv(self, products: List[BeerProduct], filename: str = None):
        """Save products to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scrapegraph_beer_products_{timestamp}.csv"

        logger.info(f"Saving {len(products)} products to {filename}")

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            if products:
                fieldnames = list(asdict(products[0]).keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for product in products:
                    writer.writerow(asdict(product))

        logger.info(f"Successfully saved to {filename}")
        return filename

    def save_to_json(self, products: List[BeerProduct], filename: str = None):
        """Save products to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scrapegraph_beer_products_{timestamp}.json"

        logger.info(f"Saving {len(products)} products to {filename}")

        products_dict = [asdict(product) for product in products]

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(products_dict, jsonfile, indent=2, ensure_ascii=False)

        logger.info(f"Successfully saved to {filename}")
        return filename

    def analyze_products(self, products: List[BeerProduct]):
        """Analyze scraped products and print statistics"""
        if not products:
            print("No products to analyze")
            return

        print(f"\n📊 BEER SCRAPING ANALYSIS")
        print("=" * 50)
        print(f"Total individual beer entries: {len(products)}")

        # Retailer distribution
        retailers = {}
        brands = {}
        pack_sizes = {}
        volumes = {}
        prices = []

        for product in products:
            retailers[product.retailer] = retailers.get(product.retailer, 0) + 1
            brands[product.brand] = brands.get(product.brand, 0) + 1
            pack_sizes[product.pack_size] = pack_sizes.get(product.pack_size, 0) + 1

            if product.volume_ml:
                volumes[product.volume_ml] = volumes.get(product.volume_ml, 0) + 1

            if product.price:
                prices.append(product.price)

        print(f"\n🏪 Retailer Distribution:")
        for retailer, count in sorted(retailers.items(), key=lambda x: x[1], reverse=True):
            print(f"   {retailer}: {count} products")

        print(f"\n🍺 Top 10 Brands:")
        top_brands = sorted(brands.items(), key=lambda x: x[1], reverse=True)[:10]
        for brand, count in top_brands:
            print(f"   {brand}: {count} products")

        print(f"\n📦 Pack Size Distribution:")
        for pack_size, count in sorted(pack_sizes.items()):
            print(f"   {pack_size}: {count} products")

        if volumes:
            print(f"\n🥤 Volume Distribution:")
            top_volumes = sorted(volumes.items(), key=lambda x: x[1], reverse=True)[:5]
            for volume, count in top_volumes:
                print(f"   {volume}ml: {count} products")

        if prices:
            print(f"\n💰 Price Statistics:")
            print(f"   Average price: £{sum(prices)/len(prices):.2f}")
            print(f"   Price range: £{min(prices):.2f} - £{max(prices):.2f}")

def main():
    """Main function to run the Scrapegraph AI beer scraper"""
    print("🍺 SCRAPEGRAPH AI BEER SCRAPER 🍺")
    print("=" * 60)
    print("Advanced AI-powered beer product scraping")
    print("Creates individual entries for each beer in multi-packs")

    # Check if user wants to use local LLM or OpenAI
    use_local = input("\nUse local Ollama model? (y/n, default=y): ").lower().strip()
    use_local_llm = use_local != 'n'

    if use_local_llm:
        print("\n⚠️  Make sure Ollama is running with llama3.2 model:")
        print("   ollama pull llama3.2")
        print("   ollama serve")
        input("Press Enter when ready...")
    else:
        if not os.getenv("OPENAI_API_KEY"):
            print("\n❌ OPENAI_API_KEY environment variable not set!")
            print("Set it with: export OPENAI_API_KEY='your-api-key'")
            return

    try:
        # Initialize scraper
        scraper = ScrapegraphBeerScraper(use_local_llm=use_local_llm)

        # Run comprehensive scraping
        print(f"\n🚀 Starting comprehensive beer scraping...")
        products = scraper.run_comprehensive_scrape()

        if not products:
            print("❌ No products found!")
            return

        print(f"\n✅ Successfully scraped {len(products)} individual beer entries!")

        # Save results
        csv_file = scraper.save_to_csv(products)
        json_file = scraper.save_to_json(products)

        # Analyze results
        scraper.analyze_products(products)

        print(f"\n📁 Files saved:")
        print(f"   CSV: {csv_file}")
        print(f"   JSON: {json_file}")
        print(f"   Log: scrapegraph_beer_scraper.log")

        print(f"\n🎉 Scraping completed successfully!")

    except KeyboardInterrupt:
        print("\n⚠️  Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"❌ Error occurred: {e}")
        print("Check scrapegraph_beer_scraper.log for details")

if __name__ == "__main__":
    main()
