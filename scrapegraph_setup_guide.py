#!/usr/bin/env python3
"""
Setup Guide for Scrapegraph AI Beer Scraper
===========================================

This script provides setup instructions and creates a working demo
of the beer scraper using Scrapegraph AI.
"""

import os
import json
from datetime import datetime

def print_setup_instructions():
    """Print comprehensive setup instructions"""
    
    print("🍺 SCRAPEGRAPH AI BEER SCRAPER SETUP GUIDE")
    print("=" * 60)
    
    print("\n📋 PREREQUISITES:")
    print("✅ Python 3.10+ (you have this)")
    print("✅ Scrapegraph AI installed (you have this)")
    print("✅ Playwright installed (you have this)")
    
    print("\n🤖 AI MODEL OPTIONS (choose one):")
    
    print("\n🔥 OPTION 1: Local Ollama (Recommended - Free)")
    print("   1. Install Ollama:")
    print("      - Download from: https://ollama.ai/")
    print("      - Or use: winget install Ollama.Ollama")
    print("   ")
    print("   2. Start Ollama service:")
    print("      ollama serve")
    print("   ")
    print("   3. Pull the model:")
    print("      ollama pull llama3.2")
    print("   ")
    print("   4. Verify installation:")
    print("      ollama list")
    
    print("\n☁️  OPTION 2: OpenAI API (Paid)")
    print("   1. Get API key from: https://platform.openai.com/api-keys")
    print("   2. Set environment variable:")
    print("      set OPENAI_API_KEY=your-api-key-here")
    print("   3. Or create .env file with:")
    print("      OPENAI_API_KEY=your-api-key-here")
    
    print("\n🚀 QUICK START:")
    print("   1. Choose and setup one of the AI options above")
    print("   2. Run: python test_environment.py")
    print("   3. If successful, run: python scrapegraph_beer_scraper.py")
    
    print("\n💡 DEMO MODE:")
    print("   If you want to see how the scraper works without AI setup,")
    print("   run the demo with sample data:")
    print("   python demo_scraper.py")

def create_sample_scrapegraph_output():
    """Create sample output showing what Scrapegraph AI would produce"""
    
    print("\n🎪 DEMO: Sample Scrapegraph AI Output")
    print("=" * 50)
    print("This shows what the AI scraper would extract from a beer webpage:")
    
    sample_ai_output = [
        {
            "name": "Stella Artois Premium Lager Beer 4 x 440ml",
            "brand": "Stella Artois",
            "pack_size": "4 pack",
            "volume_ml": 440,
            "alcohol_content": 5.0,
            "price": 4.50,
            "price_per_unit": 1.13,
            "product_url": "https://www.tesco.com/groceries/en-GB/products/123456",
            "image_url": "https://example.com/stella.jpg",
            "availability": "Available",
            "description": "Premium Belgian lager with distinctive taste"
        },
        {
            "name": "Heineken Premium Lager Beer 12 x 330ml",
            "brand": "Heineken",
            "pack_size": "12 pack", 
            "volume_ml": 330,
            "alcohol_content": 5.0,
            "price": 12.00,
            "price_per_unit": 1.00,
            "product_url": "https://www.tesco.com/groceries/en-GB/products/789012",
            "image_url": "https://example.com/heineken.jpg",
            "availability": "Available",
            "description": "World famous Dutch premium lager"
        },
        {
            "name": "Corona Extra Premium Lager Beer 6 x 330ml",
            "brand": "Corona",
            "pack_size": "6 pack",
            "volume_ml": 330,
            "alcohol_content": 4.5,
            "price": 7.50,
            "price_per_unit": 1.25,
            "product_url": "https://www.tesco.com/groceries/en-GB/products/345678",
            "image_url": "https://example.com/corona.jpg",
            "availability": "Available",
            "description": "Mexican beer with lime, light and refreshing"
        }
    ]
    
    print("\n📊 AI Extracted Data:")
    print(json.dumps(sample_ai_output, indent=2))
    
    print("\n🔄 Individual Entry Creation:")
    print("The scraper then creates individual entries for each beer:")
    
    individual_entries = []
    for product in sample_ai_output:
        pack_size = product["pack_size"]
        if "pack" in pack_size and pack_size != "1 pack":
            # Extract number from pack size
            pack_count = int(pack_size.split()[0])
            for i in range(pack_count):
                individual_entry = product.copy()
                individual_entry["name"] = f"{product['name']} (Unit {i+1} of {pack_count})"
                individual_entry["pack_size"] = "1 unit"
                individual_entry["price"] = product["price_per_unit"]
                individual_entries.append(individual_entry)
        else:
            individual_entries.append(product)
    
    print(f"\n📈 Results:")
    print(f"Original products: {len(sample_ai_output)}")
    print(f"Individual beer entries: {len(individual_entries)}")
    
    print(f"\n🍻 Sample Individual Entries:")
    for i, entry in enumerate(individual_entries[:6]):  # Show first 6
        print(f"{i+1}. {entry['name']} - £{entry['price']}")
    
    if len(individual_entries) > 6:
        print(f"... and {len(individual_entries) - 6} more entries")
    
    return individual_entries

def show_comparison():
    """Show comparison between traditional scraping and AI scraping"""
    
    print("\n⚔️  TRADITIONAL SCRAPING vs SCRAPEGRAPH AI")
    print("=" * 60)
    
    print("\n🔧 Traditional Scraping:")
    print("   ❌ Brittle - breaks when HTML changes")
    print("   ❌ Requires manual XPath/CSS selector maintenance")
    print("   ❌ Struggles with dynamic content")
    print("   ❌ Hard to extract semantic meaning")
    print("   ❌ Needs constant updates for new sites")
    
    print("\n🤖 Scrapegraph AI:")
    print("   ✅ Intelligent - understands page content")
    print("   ✅ Adapts to HTML structure changes")
    print("   ✅ Handles dynamic content naturally")
    print("   ✅ Extracts semantic information")
    print("   ✅ Works across different sites with same prompt")
    print("   ✅ Can understand context and relationships")
    
    print("\n🎯 For Beer Scraping:")
    print("   ✅ Automatically identifies beer products")
    print("   ✅ Understands pack sizes and volumes")
    print("   ✅ Extracts prices and calculates per-unit costs")
    print("   ✅ Recognizes brands and product variants")
    print("   ✅ Handles different retailer layouts")

def main():
    """Main function"""
    
    print_setup_instructions()
    
    print("\n" + "="*60)
    
    sample_entries = create_sample_scrapegraph_output()
    
    print("\n" + "="*60)
    
    show_comparison()
    
    print("\n🎉 NEXT STEPS:")
    print("1. Set up either Ollama or OpenAI API (see instructions above)")
    print("2. Run: python test_environment.py")
    print("3. When ready: python scrapegraph_beer_scraper.py")
    
    print("\n💾 Want to see the demo with sample data?")
    print("Run: python demo_scraper.py")
    
    # Save sample data for reference
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"sample_scrapegraph_output_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(sample_entries, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 Sample data saved to: {filename}")

if __name__ == "__main__":
    main()
