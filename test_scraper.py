#!/usr/bin/env python3
"""
Quick test script for the beer scraper
"""

from comprehensive_beer_scraper import BeerScraper
import logging

# Reduce log noise for test
logging.getLogger().setLevel(logging.WARNING)

def test_scraper():
    scraper = BeerScraper()
    print('🍺 Testing Beer Scraper...')
    
    try:
        # Test just the first page
        import requests
        from scrapy import Selector
        
        url = 'https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer?page=1'
        print(f'Fetching: {url}')
        
        response = scraper.session.get(url, timeout=10)
        print(f'Response status: {response.status_code}')
        
        if response.status_code == 200:
            sel = Selector(text=response.text)
            product_items = sel.xpath('//li[contains(@class, "product-list--list-item")]')
            print(f'Found {len(product_items)} product items on first page')
            
            if product_items:
                # Test parsing first few products
                for i, item in enumerate(product_items[:3]):
                    product = scraper.parse_tesco_product(item)
                    if product:
                        print(f'\n✅ Product {i+1}:')
                        print(f'   Name: {product.name}')
                        print(f'   Brand: {product.brand}')
                        print(f'   Pack size: {product.pack_size}')
                        print(f'   Price: £{product.price}')
                        print(f'   Volume: {product.volume_ml}ml' if product.volume_ml else '   Volume: Unknown')
                    else:
                        print(f'❌ Failed to parse product {i+1}')
                        
                print(f'\n🎉 Test completed successfully!')
            else:
                print('❌ No product items found - HTML structure may have changed')
        else:
            print(f'❌ Failed to fetch page: {response.status_code}')
            
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_scraper()
