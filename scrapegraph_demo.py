#!/usr/bin/env python3
"""
Simple demo of Scrapegraph AI for beer scraping
"""

import os
import json
from scrapegraphai.graphs import SmartScraperGraph

def test_scrapegraph_basic():
    """Test basic Scrapegraph AI functionality"""
    
    print("🍺 Testing Scrapegraph AI Basic Functionality")
    print("=" * 50)
    
    # Simple configuration for testing
    graph_config = {
        "llm": {
            "model": "ollama/llama3.2",
            "temperature": 0.1,
            "format": "json",
            "model_tokens": 4096,
            "base_url": "http://localhost:11434",
        },
        "verbose": True,
        "headless": True,
    }
    
    # Simple prompt for testing
    prompt = """
    Extract information about beer products from this webpage.
    For each beer product, provide:
    - Product name
    - Brand
    - Price
    - Pack size (if mentioned)
    - Volume (if mentioned)
    
    Return as a JSON array of products.
    """
    
    # Test URL - using a simple beer page
    test_url = "https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer"
    
    try:
        print(f"Testing with URL: {test_url}")
        print("Creating SmartScraperGraph...")
        
        smart_scraper = SmartScraperGraph(
            prompt=prompt,
            source=test_url,
            config=graph_config
        )
        
        print("Running scraper...")
        result = smart_scraper.run()
        
        print(f"\n✅ Scraping completed!")
        print(f"Result type: {type(result)}")
        
        if result:
            print(f"\n📊 Results:")
            print(json.dumps(result, indent=2))
        else:
            print("❌ No results returned")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure Ollama is running: ollama serve")
        print("2. Make sure llama3.2 model is installed: ollama pull llama3.2")
        print("3. Check if the URL is accessible")

def test_with_openai():
    """Test with OpenAI if API key is available"""
    
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("⚠️  OPENAI_API_KEY not set, skipping OpenAI test")
        return
    
    print("\n🤖 Testing with OpenAI GPT-4o-mini")
    print("=" * 50)
    
    graph_config = {
        "llm": {
            "api_key": openai_key,
            "model": "openai/gpt-4o-mini",
            "temperature": 0.1,
        },
        "verbose": True,
        "headless": True,
    }
    
    prompt = """
    Find beer products on this page and extract:
    - Product name
    - Brand
    - Price in GBP
    - Pack size
    - Volume per unit
    
    Return as JSON array.
    """
    
    test_url = "https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer"
    
    try:
        print(f"Testing with URL: {test_url}")
        
        smart_scraper = SmartScraperGraph(
            prompt=prompt,
            source=test_url,
            config=graph_config
        )
        
        print("Running OpenAI scraper...")
        result = smart_scraper.run()
        
        print(f"\n✅ OpenAI scraping completed!")
        
        if result:
            print(f"\n📊 OpenAI Results:")
            print(json.dumps(result, indent=2))
        else:
            print("❌ No results from OpenAI")
            
    except Exception as e:
        print(f"❌ OpenAI Error: {e}")

if __name__ == "__main__":
    print("🍺 SCRAPEGRAPH AI DEMO 🍺")
    print("Testing Scrapegraph AI for beer scraping")
    print()
    
    # Test basic functionality with Ollama
    test_scrapegraph_basic()
    
    # Test with OpenAI if available
    test_with_openai()
    
    print("\n🎉 Demo completed!")
    print("\nIf the demo worked, you can now run the full scraper:")
    print("python scrapegraph_beer_scraper.py")
