#!/usr/bin/env python3
"""
Demo script showing how the beer scraper works with sample data
"""

from comprehensive_beer_scraper import BeerProduct, BeerScraper
from datetime import datetime
import json
import csv

def create_sample_beer_data():
    """Create sample beer product data to demonstrate functionality"""
    
    sample_products = [
        BeerProduct(
            name="Stella Artois Premium Lager Beer 4 x 440ml",
            brand="Stella Artois",
            pack_size="4 pack",
            volume_ml=440,
            alcohol_content=5.0,
            price=4.50,
            price_per_unit=1.13,
            retailer="Tesco",
            product_url="https://www.tesco.com/groceries/en-GB/products/123456",
            image_url="https://example.com/stella.jpg",
            availability="Available",
            category="Beer",
            scraped_at=datetime.now().isoformat()
        ),
        BeerProduct(
            name="Heineken Premium Lager Beer 12 x 330ml",
            brand="Heineken",
            pack_size="12 pack",
            volume_ml=330,
            alcohol_content=5.0,
            price=12.00,
            price_per_unit=1.00,
            retailer="Tesco",
            product_url="https://www.tesco.com/groceries/en-GB/products/789012",
            image_url="https://example.com/heineken.jpg",
            availability="Available",
            category="Beer",
            scraped_at=datetime.now().isoformat()
        ),
        BeerProduct(
            name="Budweiser King of Beers Lager Beer 500ml",
            brand="Budweiser",
            pack_size="1 unit",
            volume_ml=500,
            alcohol_content=4.5,
            price=1.75,
            price_per_unit=1.75,
            retailer="Tesco",
            product_url="https://www.tesco.com/groceries/en-GB/products/345678",
            image_url="https://example.com/budweiser.jpg",
            availability="Available",
            category="Beer",
            scraped_at=datetime.now().isoformat()
        ),
        BeerProduct(
            name="Corona Extra Premium Lager Beer 6 x 330ml",
            brand="Corona",
            pack_size="6 pack",
            volume_ml=330,
            alcohol_content=4.5,
            price=7.50,
            price_per_unit=1.25,
            retailer="Tesco",
            product_url="https://www.tesco.com/groceries/en-GB/products/901234",
            image_url="https://example.com/corona.jpg",
            availability="Available",
            category="Beer",
            scraped_at=datetime.now().isoformat()
        ),
        BeerProduct(
            name="Guinness Draught Stout Beer 4 x 440ml",
            brand="Guinness",
            pack_size="4 pack",
            volume_ml=440,
            alcohol_content=4.2,
            price=5.25,
            price_per_unit=1.31,
            retailer="Tesco",
            product_url="https://www.tesco.com/groceries/en-GB/products/567890",
            image_url="https://example.com/guinness.jpg",
            availability="Available",
            category="Beer",
            scraped_at=datetime.now().isoformat()
        ),
        BeerProduct(
            name="Peroni Nastro Azzurro Premium Lager Beer 24 x 330ml",
            brand="Peroni",
            pack_size="24 pack",
            volume_ml=330,
            alcohol_content=5.1,
            price=22.00,
            price_per_unit=0.92,
            retailer="Tesco",
            product_url="https://www.tesco.com/groceries/en-GB/products/234567",
            image_url="https://example.com/peroni.jpg",
            availability="Available",
            category="Beer",
            scraped_at=datetime.now().isoformat()
        )
    ]
    
    return sample_products

def demo_individual_entries():
    """Demonstrate creating individual entries for multi-pack products"""
    print("🍺 Beer Scraper Demo - Individual Entries Feature")
    print("=" * 60)
    
    # Create sample data
    sample_products = create_sample_beer_data()
    
    print(f"📦 Original products: {len(sample_products)}")
    for i, product in enumerate(sample_products, 1):
        print(f"{i}. {product.name} - {product.pack_size}")
    
    # Create individual entries
    scraper = BeerScraper()
    individual_products = scraper.create_individual_entries(sample_products)
    
    print(f"\n🍻 Individual entries: {len(individual_products)}")
    
    # Group by original product for display
    current_base = ""
    for product in individual_products:
        base_name = product.name.split(" (Unit")[0] if "(Unit" in product.name else product.name
        if base_name != current_base:
            print(f"\n📋 {base_name}:")
            current_base = base_name
        
        if "(Unit" in product.name:
            unit_info = product.name.split("(Unit")[1].rstrip(")")
            print(f"   • Unit {unit_info} - £{product.price}")
        else:
            print(f"   • Single unit - £{product.price}")
    
    return individual_products

def demo_export_functionality(products):
    """Demonstrate export functionality"""
    print(f"\n💾 Export Demo")
    print("=" * 30)
    
    scraper = BeerScraper()
    
    # Save to CSV
    csv_filename = "demo_beer_products.csv"
    scraper.save_to_csv(products, csv_filename)
    print(f"✅ Saved to CSV: {csv_filename}")
    
    # Save to JSON
    json_filename = "demo_beer_products.json"
    scraper.save_to_json(products, json_filename)
    print(f"✅ Saved to JSON: {json_filename}")
    
    # Show file contents preview
    print(f"\n📄 CSV Preview (first 5 lines):")
    try:
        with open(csv_filename, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i < 5:
                    print(f"   {line.strip()}")
                else:
                    break
    except Exception as e:
        print(f"   Error reading CSV: {e}")

def demo_analysis():
    """Demonstrate analysis functionality"""
    print(f"\n📊 Analysis Demo")
    print("=" * 30)
    
    # Create sample data
    products = create_sample_beer_data()
    scraper = BeerScraper()
    individual_products = scraper.create_individual_entries(products)
    
    # Basic statistics
    total_products = len(individual_products)
    brands = {}
    pack_sizes = {}
    price_range = []
    
    for product in individual_products:
        brands[product.brand] = brands.get(product.brand, 0) + 1
        pack_sizes[product.pack_size] = pack_sizes.get(product.pack_size, 0) + 1
        if product.price:
            price_range.append(product.price)
    
    print(f"📈 Statistics:")
    print(f"   Total products: {total_products}")
    print(f"   Unique brands: {len(brands)}")
    print(f"   Price range: £{min(price_range):.2f} - £{max(price_range):.2f}")
    print(f"   Average price: £{sum(price_range)/len(price_range):.2f}")
    
    print(f"\n🏷️  Brand distribution:")
    for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True):
        print(f"   {brand}: {count} products")
    
    print(f"\n📦 Pack size distribution:")
    for pack_size, count in sorted(pack_sizes.items()):
        print(f"   {pack_size}: {count} products")

def main():
    """Run the complete demo"""
    print("🍺 COMPREHENSIVE BEER SCRAPER DEMO 🍺")
    print("=" * 50)
    print("This demo shows how the scraper works with sample data")
    print("since live scraping may be blocked by retailers.\n")
    
    # Demo individual entries feature
    individual_products = demo_individual_entries()
    
    # Demo export functionality
    demo_export_functionality(individual_products)
    
    # Demo analysis
    demo_analysis()
    
    print(f"\n🎉 Demo completed!")
    print(f"\nTo run the actual scraper (when network allows):")
    print(f"   python comprehensive_beer_scraper.py")
    print(f"\nGenerated files:")
    print(f"   - demo_beer_products.csv")
    print(f"   - demo_beer_products.json")

if __name__ == "__main__":
    main()
