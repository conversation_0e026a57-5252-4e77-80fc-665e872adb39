# 🍺 Comprehensive Beer Product Scraper

A Python script that creates an **exhaustive list of all beer products** from major UK retailers, treating each pack size and variant as individual entries.

## 🎯 Features

- **Multi-retailer support**: Scrapes from Tesco, ASDA, Sainsbury's, and more
- **Individual entries**: Each pack size (1-pack, 4-pack, 12-pack, etc.) becomes a separate entry
- **Detailed extraction**: Product names, brands, pack sizes, volumes, alcohol content, prices
- **Multiple formats**: Exports to both CSV and JSON
- **Robust scraping**: Error handling, retry logic, and respectful rate limiting
- **Price analysis**: Calculates price per unit for multi-packs
- **Comprehensive logging**: Detailed logs for debugging and monitoring

## 🚀 Quick Start

### Installation

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   # Using conda (recommended since you're using conda base environment)
   conda activate base
   pip install -r requirements.txt
   
   # Or using pip directly
   pip install requests scrapy lxml pandas
   ```

### Basic Usage

```bash
# Run the comprehensive scraper
python comprehensive_beer_scraper.py

# Analyze existing results
python comprehensive_beer_scraper.py --analyze exhaustive_beer_list_20250614_123456.csv
```

## 📊 Output Files

The scraper generates several files:

- **`exhaustive_beer_list_YYYYMMDD_HHMMSS.csv`**: Main results in CSV format
- **`exhaustive_beer_list_YYYYMMDD_HHMMSS.json`**: Main results in JSON format  
- **`beer_scraper.log`**: Detailed scraping logs

## 🍻 Product Data Structure

Each beer product entry contains:

| Field | Description | Example |
|-------|-------------|---------|
| `name` | Full product name | "Stella Artois Lager Beer 4 x 440ml" |
| `brand` | Beer brand | "Stella Artois" |
| `pack_size` | Pack configuration | "4 pack" or "1 unit" |
| `volume_ml` | Volume per unit in ml | 440 |
| `alcohol_content` | Alcohol percentage | 5.0 |
| `price` | Price in GBP | 4.50 |
| `price_per_unit` | Price per individual unit | 1.13 |
| `retailer` | Source retailer | "Tesco" |
| `product_url` | Direct product link | "https://..." |
| `availability` | Stock status | "Available" |
| `scraped_at` | Timestamp | "2025-06-14T12:34:56" |

## 🔧 Advanced Usage

### Command Line Options

```bash
# Scrape specific retailers only
python comprehensive_beer_scraper.py --retailers tesco asda

# Don't create individual entries for multi-packs
python comprehensive_beer_scraper.py --no-individual

# Analyze existing data
python comprehensive_beer_scraper.py --analyze my_beer_data.csv
```

### Customization

The scraper is designed to be easily extensible:

1. **Add new retailers**: Implement new `scrape_[retailer]_beer()` methods
2. **Modify extraction**: Update the parsing logic in `parse_[retailer]_product()`
3. **Change output format**: Modify the `save_to_*()` methods
4. **Adjust rate limiting**: Change the `delay` parameter

## 📈 Example Output

```
🍺 Comprehensive Beer Product Scraper 🍺
==================================================
Scraping Tesco page 1...
Scraping Tesco page 2...
...
✅ Successfully scraped 1,247 beer products!

📊 Summary Statistics:
Total products: 1,247
Retailers: 1
  - Tesco: 1,247 products
Brands: 89
  - Stella Artois: 45 products
  - Heineken: 38 products
  - Budweiser: 32 products
Pack sizes: 12
  - 1 unit: 623 products
  - 4 pack: 312 products
  - 12 pack: 156 products

🍻 Sample Products:
1. Stella Artois Lager Beer 4 x 440ml (Unit 1 of 4)
   Brand: Stella Artois | Pack: 1 unit
   Price: £1.13 | Retailer: Tesco
   Volume: 440ml | Alcohol: 5.0%
```

## ⚠️ Important Notes

- **Respectful scraping**: Built-in delays and error handling to avoid overwhelming servers
- **Terms of service**: Ensure compliance with retailer terms of service
- **Rate limiting**: Default 1.5-second delay between requests
- **Error handling**: Continues scraping even if individual products fail
- **Data accuracy**: Product information is extracted automatically and may need verification

## 🛠️ Troubleshooting

### Common Issues

1. **Import errors**: Make sure all dependencies are installed
2. **Network timeouts**: Check internet connection and try again
3. **Empty results**: Retailers may have changed their HTML structure
4. **Permission errors**: Ensure write permissions for output files

### Debugging

Check the `beer_scraper.log` file for detailed error messages and scraping progress.

## 🤝 Contributing

Feel free to:
- Add support for new retailers
- Improve product information extraction
- Enhance error handling
- Add new export formats

## 📝 License

This tool is for educational and research purposes. Please respect retailer terms of service and use responsibly.

---

**Happy beer hunting! 🍻**
