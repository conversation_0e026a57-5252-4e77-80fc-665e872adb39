#!/usr/bin/env python3
"""
Comprehensive Beer Product Scraper
==================================

This script scrapes beer products from multiple UK retailers and creates
an exhaustive list where each pack size is treated as an individual entry.

Features:
- Multi-retailer support (Tesco, ASDA, Sainsbury's)
- Individual entries for different pack sizes
- Detailed product information extraction
- CSV and JSON export capabilities
- Robust error handling and retry logic
- Rate limiting to be respectful to servers

Author: Beer Scraping Bot
Date: 2025-06-14
"""

import requests
from scrapy import Selector
import time
import csv
import json
import re
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('beer_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class BeerProduct:
    """Data class to represent a beer product"""
    name: str
    brand: str
    pack_size: str
    volume_ml: Optional[int]
    alcohol_content: Optional[float]
    price: Optional[float]
    price_per_unit: Optional[float]
    retailer: str
    product_url: str
    image_url: Optional[str]
    availability: str
    category: str
    scraped_at: str

class BeerScraper:
    """Main scraper class for beer products"""
    
    def __init__(self):
        self.session = requests.Session()
        # Rotate through different user agents to avoid detection
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
        self.current_ua_index = 0
        self.update_headers()
        self.products = []
        self.delay = 2.0  # Increased delay to be more respectful
        self.timeout = 15  # Increased timeout

    def update_headers(self):
        """Update session headers with current user agent"""
        self.session.headers.update({
            'User-Agent': self.user_agents[self.current_ua_index],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-GB,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
        })

    def rotate_user_agent(self):
        """Rotate to next user agent"""
        self.current_ua_index = (self.current_ua_index + 1) % len(self.user_agents)
        self.update_headers()
        
    def extract_pack_info(self, text: str) -> Dict[str, any]:
        """Extract pack size and volume information from product text"""
        pack_info = {
            'pack_size': '1 unit',
            'volume_ml': None,
            'unit_count': 1
        }
        
        if not text:
            return pack_info
            
        text = text.lower()
        
        # Extract pack count (e.g., "4 pack", "12x", "24 cans")
        pack_patterns = [
            r'(\d+)\s*(?:x|pack|cans?|bottles?)',
            r'(\d+)\s*(?:-|–)\s*pack',
            r'pack\s*of\s*(\d+)',
            r'multipack\s*(\d+)'
        ]
        
        for pattern in pack_patterns:
            match = re.search(pattern, text)
            if match:
                pack_info['unit_count'] = int(match.group(1))
                pack_info['pack_size'] = f"{match.group(1)} pack"
                break
        
        # Extract volume (e.g., "500ml", "0.5l", "330ml")
        volume_patterns = [
            r'(\d+(?:\.\d+)?)\s*ml',
            r'(\d+(?:\.\d+)?)\s*l(?:itre)?s?',
            r'(\d+(?:\.\d+)?)\s*cl'
        ]
        
        for pattern in volume_patterns:
            match = re.search(pattern, text)
            if match:
                volume = float(match.group(1))
                if 'cl' in pattern:
                    volume *= 10  # Convert cl to ml
                elif 'l' in pattern and volume < 10:  # Likely liters
                    volume *= 1000  # Convert l to ml
                pack_info['volume_ml'] = int(volume)
                break
                
        return pack_info
    
    def extract_alcohol_content(self, text: str) -> Optional[float]:
        """Extract alcohol content percentage from text"""
        if not text:
            return None
            
        # Look for patterns like "4.5%", "5.0% ABV", "4.2% vol"
        pattern = r'(\d+(?:\.\d+)?)\s*%\s*(?:abv|vol|alcohol)?'
        match = re.search(pattern, text.lower())
        
        if match:
            return float(match.group(1))
        return None
    
    def extract_price(self, price_text: str) -> Optional[float]:
        """Extract price from price text"""
        if not price_text:
            return None
            
        # Remove currency symbols and extract number
        price_pattern = r'£?(\d+(?:\.\d{2})?)'
        match = re.search(price_pattern, price_text.replace(',', ''))
        
        if match:
            return float(match.group(1))
        return None

    def scrape_tesco_beer(self) -> List[BeerProduct]:
        """Scrape beer products from Tesco"""
        logger.info("Starting Tesco beer scraping...")
        base_url = 'https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer'
        products = []
        
        page = 1
        max_pages = 50  # Safety limit
        
        while page <= max_pages:
            url = f"{base_url}?page={page}"
            logger.info(f"Scraping Tesco page {page}...")
            
            try:
                # Try multiple times with different user agents if needed
                response = None
                for attempt in range(3):
                    try:
                        response = self.session.get(url, timeout=self.timeout)
                        response.raise_for_status()
                        break
                    except (requests.RequestException, requests.Timeout) as e:
                        logger.warning(f"Attempt {attempt + 1} failed for page {page}: {e}")
                        if attempt < 2:  # Don't rotate on last attempt
                            self.rotate_user_agent()
                            time.sleep(self.delay * (attempt + 1))  # Exponential backoff
                        else:
                            raise e

                if not response:
                    logger.error(f"Failed to get response for page {page} after 3 attempts")
                    break

                sel = Selector(text=response.text)

                # Try multiple selectors in case the HTML structure varies
                product_items = sel.xpath('//li[contains(@class, "product-list--list-item")]')
                if not product_items:
                    product_items = sel.xpath('//div[contains(@class, "product-tile")]')
                if not product_items:
                    product_items = sel.xpath('//article[contains(@class, "product")]')

                if not product_items:
                    logger.info(f"No product items found on page {page} - checking if we've reached the end")
                    # Check if this is actually the end or if the structure changed
                    if "no results" in response.text.lower() or "no products" in response.text.lower():
                        logger.info("Reached end of products")
                        break
                    else:
                        logger.warning(f"No products found but page seems valid - HTML structure may have changed")
                        break

                logger.info(f"Found {len(product_items)} product items on page {page}")

                for item in product_items:
                    try:
                        product = self.parse_tesco_product(item)
                        if product:
                            products.append(product)
                    except Exception as e:
                        logger.warning(f"Error parsing Tesco product: {e}")
                        continue

                page += 1
                time.sleep(self.delay)

            except requests.RequestException as e:
                logger.error(f"Error fetching Tesco page {page}: {e}")
                # Try a few more pages in case it's a temporary issue
                if page <= 3:  # Only retry for first few pages
                    logger.info("Retrying after network error...")
                    time.sleep(self.delay * 2)
                    continue
                else:
                    break
            except Exception as e:
                logger.error(f"Unexpected error on Tesco page {page}: {e}")
                break
        
        logger.info(f"Scraped {len(products)} products from Tesco")
        return products
    
    def parse_tesco_product(self, item) -> Optional[BeerProduct]:
        """Parse individual Tesco product"""
        try:
            # Extract basic info
            name_elem = item.xpath('.//a[contains(@class, "product-tile--title")]/@title').get()
            if not name_elem:
                name_elem = item.xpath('.//a[contains(@class, "product-tile--title")]/text()').get()
            
            if not name_elem:
                return None
                
            name = name_elem.strip()
            
            # Extract product URL
            product_url = item.xpath('.//a[contains(@class, "product-tile--title")]/@href').get()
            if product_url:
                product_url = urljoin('https://www.tesco.com', product_url)
            
            # Extract price
            price_text = item.xpath('.//span[contains(@class, "value")]/text()').get()
            price = self.extract_price(price_text) if price_text else None
            
            # Extract image URL
            image_url = item.xpath('.//img/@src').get()
            if image_url and not image_url.startswith('http'):
                image_url = urljoin('https://www.tesco.com', image_url)
            
            # Extract pack information from name and description
            pack_info = self.extract_pack_info(name)
            
            # Extract brand (usually first word or two)
            brand_match = re.match(r'^([A-Za-z\s&]+)', name)
            brand = brand_match.group(1).strip() if brand_match else "Unknown"
            
            # Extract alcohol content
            alcohol_content = self.extract_alcohol_content(name)
            
            # Calculate price per unit if we have pack info
            price_per_unit = None
            if price and pack_info['unit_count'] > 0:
                price_per_unit = round(price / pack_info['unit_count'], 2)
            
            return BeerProduct(
                name=name,
                brand=brand,
                pack_size=pack_info['pack_size'],
                volume_ml=pack_info['volume_ml'],
                alcohol_content=alcohol_content,
                price=price,
                price_per_unit=price_per_unit,
                retailer="Tesco",
                product_url=product_url or "",
                image_url=image_url,
                availability="Available",
                category="Beer",
                scraped_at=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.warning(f"Error parsing Tesco product: {e}")
            return None

    def scrape_asda_beer(self) -> List[BeerProduct]:
        """Scrape beer products from ASDA"""
        logger.info("Starting ASDA beer scraping...")
        base_url = 'https://groceries.asda.com/search/beer'
        products = []

        try:
            response = self.session.get(base_url, timeout=10)
            response.raise_for_status()

            sel = Selector(text=response.text)
            # ASDA uses different selectors - this is a basic implementation
            product_items = sel.xpath('//div[contains(@class, "product-item")]')

            for item in product_items:
                try:
                    product = self.parse_asda_product(item)
                    if product:
                        products.append(product)
                except Exception as e:
                    logger.warning(f"Error parsing ASDA product: {e}")
                    continue

            time.sleep(self.delay)

        except requests.RequestException as e:
            logger.error(f"Error fetching ASDA products: {e}")
        except Exception as e:
            logger.error(f"Unexpected error scraping ASDA: {e}")

        logger.info(f"Scraped {len(products)} products from ASDA")
        return products

    def parse_asda_product(self, item) -> Optional[BeerProduct]:
        """Parse individual ASDA product - placeholder implementation"""
        # This would need to be customized based on ASDA's actual HTML structure
        try:
            name = item.xpath('.//h3/text()').get()
            if not name:
                return None

            price_text = item.xpath('.//span[contains(@class, "price")]/text()').get()
            price = self.extract_price(price_text) if price_text else None

            pack_info = self.extract_pack_info(name)
            brand_match = re.match(r'^([A-Za-z\s&]+)', name)
            brand = brand_match.group(1).strip() if brand_match else "Unknown"

            return BeerProduct(
                name=name.strip(),
                brand=brand,
                pack_size=pack_info['pack_size'],
                volume_ml=pack_info['volume_ml'],
                alcohol_content=self.extract_alcohol_content(name),
                price=price,
                price_per_unit=round(price / pack_info['unit_count'], 2) if price and pack_info['unit_count'] > 0 else None,
                retailer="ASDA",
                product_url="",
                image_url=None,
                availability="Available",
                category="Beer",
                scraped_at=datetime.now().isoformat()
            )
        except Exception as e:
            logger.warning(f"Error parsing ASDA product: {e}")
            return None

    def create_individual_entries(self, products: List[BeerProduct]) -> List[BeerProduct]:
        """Create individual entries for multi-pack products"""
        individual_products = []

        for product in products:
            pack_info = self.extract_pack_info(product.name)
            unit_count = pack_info['unit_count']

            if unit_count > 1:
                # Create individual entries for each unit in the pack
                for i in range(unit_count):
                    individual_product = BeerProduct(
                        name=f"{product.name} (Unit {i+1} of {unit_count})",
                        brand=product.brand,
                        pack_size="1 unit",
                        volume_ml=product.volume_ml,
                        alcohol_content=product.alcohol_content,
                        price=product.price_per_unit,
                        price_per_unit=product.price_per_unit,
                        retailer=product.retailer,
                        product_url=product.product_url,
                        image_url=product.image_url,
                        availability=product.availability,
                        category=product.category,
                        scraped_at=product.scraped_at
                    )
                    individual_products.append(individual_product)
            else:
                # Single unit product, add as is
                individual_products.append(product)

        return individual_products

    def save_to_csv(self, products: List[BeerProduct], filename: str = None):
        """Save products to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"beer_products_{timestamp}.csv"

        logger.info(f"Saving {len(products)} products to {filename}")

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            if products:
                fieldnames = list(asdict(products[0]).keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for product in products:
                    writer.writerow(asdict(product))

        logger.info(f"Successfully saved to {filename}")

    def save_to_json(self, products: List[BeerProduct], filename: str = None):
        """Save products to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"beer_products_{timestamp}.json"

        logger.info(f"Saving {len(products)} products to {filename}")

        products_dict = [asdict(product) for product in products]

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(products_dict, jsonfile, indent=2, ensure_ascii=False)

        logger.info(f"Successfully saved to {filename}")

    def run_comprehensive_scrape(self, individual_entries: bool = True) -> List[BeerProduct]:
        """Run comprehensive scraping across all retailers"""
        logger.info("Starting comprehensive beer product scraping...")
        all_products = []

        # Scrape from different retailers
        tesco_products = self.scrape_tesco_beer()
        all_products.extend(tesco_products)

        # Add more retailers as needed
        # asda_products = self.scrape_asda_beer()
        # all_products.extend(asda_products)

        logger.info(f"Total products scraped: {len(all_products)}")

        if individual_entries:
            logger.info("Creating individual entries for multi-pack products...")
            all_products = self.create_individual_entries(all_products)
            logger.info(f"Total individual entries: {len(all_products)}")

        return all_products

def main():
    """Main function to run the beer scraper"""
    print("🍺 Comprehensive Beer Product Scraper 🍺")
    print("=" * 50)

    scraper = BeerScraper()

    try:
        # Run comprehensive scraping
        products = scraper.run_comprehensive_scrape(individual_entries=True)

        if not products:
            print("❌ No products found!")
            return

        print(f"\n✅ Successfully scraped {len(products)} beer products!")

        # Save to files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"exhaustive_beer_list_{timestamp}.csv"
        json_filename = f"exhaustive_beer_list_{timestamp}.json"

        scraper.save_to_csv(products, csv_filename)
        scraper.save_to_json(products, json_filename)

        # Print summary statistics
        print("\n📊 Summary Statistics:")
        print(f"Total products: {len(products)}")

        retailers = {}
        brands = {}
        pack_sizes = {}

        for product in products:
            retailers[product.retailer] = retailers.get(product.retailer, 0) + 1
            brands[product.brand] = brands.get(product.brand, 0) + 1
            pack_sizes[product.pack_size] = pack_sizes.get(product.pack_size, 0) + 1

        print(f"Retailers: {len(retailers)}")
        for retailer, count in sorted(retailers.items()):
            print(f"  - {retailer}: {count} products")

        print(f"Brands: {len(brands)}")
        top_brands = sorted(brands.items(), key=lambda x: x[1], reverse=True)[:10]
        for brand, count in top_brands:
            print(f"  - {brand}: {count} products")

        print(f"Pack sizes: {len(pack_sizes)}")
        for pack_size, count in sorted(pack_sizes.items()):
            print(f"  - {pack_size}: {count} products")

        # Show sample products
        print("\n🍻 Sample Products:")
        for i, product in enumerate(products[:5]):
            print(f"{i+1}. {product.name}")
            print(f"   Brand: {product.brand} | Pack: {product.pack_size}")
            print(f"   Price: £{product.price} | Retailer: {product.retailer}")
            if product.volume_ml:
                print(f"   Volume: {product.volume_ml}ml")
            if product.alcohol_content:
                print(f"   Alcohol: {product.alcohol_content}%")
            print()

        print(f"📁 Files saved:")
        print(f"  - CSV: {csv_filename}")
        print(f"  - JSON: {json_filename}")
        print(f"  - Log: beer_scraper.log")

    except KeyboardInterrupt:
        print("\n⚠️  Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"❌ Error occurred: {e}")

def analyze_products(filename: str):
    """Analyze scraped products from a CSV file"""
    try:
        import pandas as pd

        df = pd.read_csv(filename)

        print(f"📊 Analysis of {len(df)} products:")
        print("\nTop 10 Brands:")
        print(df['brand'].value_counts().head(10))

        print("\nPrice Distribution:")
        print(df['price'].describe())

        print("\nPack Size Distribution:")
        print(df['pack_size'].value_counts())

        if 'alcohol_content' in df.columns:
            print("\nAlcohol Content Distribution:")
            print(df['alcohol_content'].describe())

    except ImportError:
        print("Install pandas for advanced analysis: pip install pandas")
    except Exception as e:
        print(f"Error analyzing products: {e}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Comprehensive Beer Product Scraper')
    parser.add_argument('--analyze', type=str, help='Analyze existing CSV file')
    parser.add_argument('--retailers', nargs='+', default=['tesco'],
                       help='Retailers to scrape (tesco, asda, sainsburys)')
    parser.add_argument('--no-individual', action='store_true',
                       help='Don\'t create individual entries for multi-packs')

    args = parser.parse_args()

    if args.analyze:
        analyze_products(args.analyze)
    else:
        main()
