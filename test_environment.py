#!/usr/bin/env python3
"""
Test environment for Scrapegraph AI
"""

import os
import requests

def test_environment():
    """Test if environment is ready for Scrapegraph AI"""
    
    print("🔍 Testing Environment for Scrapegraph AI")
    print("=" * 50)
    
    # Check OpenAI API key
    openai_key = os.getenv("OPENAI_API_KEY")
    print(f"OPENAI_API_KEY set: {bool(openai_key)}")
    
    # Test if scrapegraphai imports correctly
    try:
        from scrapegraphai.graphs import SmartScraperGraph
        print("✅ Scrapegraph AI imported successfully")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test if Ollama is accessible
    try:
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running and accessible")
            models = response.json().get('models', [])
            model_names = [m.get('name', 'unknown') for m in models]
            print(f"Available models: {model_names}")
            
            # Check if llama3.2 is available
            if any('llama3.2' in name for name in model_names):
                print("✅ llama3.2 model found")
                return True
            else:
                print("⚠️  llama3.2 model not found")
                print("Run: ollama pull llama3.2")
        else:
            print("⚠️  Ollama not responding properly")
    except Exception as e:
        print(f"❌ Ollama not accessible: {e}")
        print("Make sure Ollama is installed and running: ollama serve")
    
    # If Ollama not available, check OpenAI
    if openai_key:
        print("✅ Can use OpenAI API as fallback")
        return True
    else:
        print("❌ Neither Ollama nor OpenAI API available")
        print("Set OPENAI_API_KEY or install Ollama")
        return False

if __name__ == "__main__":
    success = test_environment()
    
    if success:
        print("\n🎉 Environment is ready!")
        print("You can now run the beer scraper")
    else:
        print("\n❌ Environment needs setup")
        print("Please fix the issues above before running the scraper")
